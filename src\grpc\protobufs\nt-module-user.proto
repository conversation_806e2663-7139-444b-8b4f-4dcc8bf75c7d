syntax = "proto3";

package nt_module_user;

message PlaceholderRequest {}

message StringDataResponse {
  int32 code = 1;
  string message = 2;
  string data = 3;
}

message FindDataByPageRequest {
  int32 pageNumber = 1;
  int32 pageSize = 2;
}

service InfoGroupService {
  rpc CreateInfoGroup(CreateInfoGroupRequest) returns (StringDataResponse) {}
  rpc DeleteInfoGroup(DeleteInfoGroupRequest) returns (StringDataResponse) {}
  rpc UpdateInfoGroup(UpdateInfoGroupRequest) returns (StringDataResponse) {}
  rpc AddInfoItemToInfoGroup(AddInfoItemToInfoGroupRequest) returns (StringDataResponse) {}
  rpc DeleteIntoItemFromInfoGroup(DeleteIntoItemFromInfoGroupRequest) returns (StringDataResponse) {}
  rpc FindAllInfoGroup(FindDataByPageRequest) returns (FindAllInfoGroupResponse) {}
  rpc FindInfoItemsByGroupId(FindInfoItemsByGroupIdRequest) returns (FindInfoItemsByGroupIdResponse) {}
}

message CreateInfoGroupRequest {
  string name = 1;
  int32 roleId = 2;
}

message DeleteInfoGroupRequest { int32 groupId = 1; }

message UpdateInfoGroupRequest {
  int32 groupId = 1;
  string name = 2;
  int32 roleId = 3;
}

message AddInfoItemToInfoGroupRequest {
  int32 infoGroupId = 1;
  repeated int32 infoItemIds = 2;
}

message DeleteIntoItemFromInfoGroupRequest {
  int32 infoGroupId = 1;
  repeated int32 infoItemIds = 2;
}

message FindAllInfoGroupResponse {
  int32 code = 1;
  string message = 2;
  repeated InfoGroup data = 3;
  int32 count = 4;
}
message InfoGroup {
  int32 id = 1;
  string name = 2;
}

message FindInfoItemsByGroupIdRequest { int32 infoGroupId = 1; }

message FindInfoItemsByGroupIdResponse {
  int32 code = 1;
  string message = 2;
  repeated InfoItem data = 3;
}
message InfoItem {
  int32 id = 1;
  string name = 2;
  string description = 3;
  string type = 4;
  bool registerDisplay = 5;
  bool informationDisplay = 6;
  int32 order = 7;
}

service InfoItemService {
  rpc CreateInfoItem(CreateInfoItemRequest) returns (StringDataResponse) {}
  rpc DeleteInfoItem(DeleteInfoItemRequest) returns (StringDataResponse) {}
  rpc UpdateInfoItem(UpdateInfoItemRequest) returns (StringDataResponse) {}
  rpc FindAllInfoItem(FindDataByPageRequest) returns (FindAllInfoItemResponse) {}
}

message CreateInfoItemRequest {
  InfoItem infoItemInput = 1;
  message InfoItemInput {
    string name = 1;
    string description = 2;
    string type = 3;
    bool registerDisplay = 4;
    bool informationDisplay = 5;
    int32 order = 6;
  }
}

message DeleteInfoItemRequest { int32 infoItemId = 1; }

message UpdateInfoItemRequest { InfoItem updateInfoItemInput = 1; }

message FindAllInfoItemResponse {
  int32 code = 1;
  string message = 2;
  repeated InfoItem data = 3;
  int32 count = 4;
}

service OrganizationService {
  rpc CreateOrganization(CreateOrganizationRequest) returns (StringDataResponse) {}
  rpc AddUsersToOrganization(AddUsersToOrganizationRequest) returns (StringDataResponse) {}
  rpc DeleteOrganization(DeleteOrganizationRequest) returns (StringDataResponse) {}
  rpc DeleteUserFromOrganization(DeleteUserFromOrganizationRequest) returns (StringDataResponse) {}
  rpc UpdateOrganization(UpdateOrganizationRequest) returns (StringDataResponse) {}
  rpc FindRootOrganizations(PlaceholderRequest) returns (FindRootOrganizationsResponse) {}
  rpc FindAllOrganizations(PlaceholderRequest) returns (StringDataResponse) {}
  rpc FindChildrenOrganizations(FindChildrenOrganizationsRequest) returns (StringDataResponse) {}
}

message CreateOrganizationRequest {
  string name = 1;
  int32 parentId = 2;
}

message AddUsersToOrganizationRequest {
  int32 id = 1;
  repeated int32 userIds = 2;
}

message DeleteOrganizationRequest { int32 id = 1; }

message DeleteUserFromOrganizationRequest {
  int32 id = 1;
  repeated int32 userIds = 2;
}

message UpdateOrganizationRequest {
  int32 id = 1;
  string name = 2;
  int32 parentId = 3;
}

message FindRootOrganizationsResponse {
  int32 code = 1;
  string message = 2;
  repeated Organization data = 3;
}
message Organization {
  int32 id = 1;
  string name = 2;
}

message FindChildrenOrganizationsRequest { int32 id = 1; }

service SystemModuleService {
  rpc FindSystemModules(FindDataByPageRequest) returns (FindSystemModuleResponse) {}
}

message FindSystemModuleResponse {
  int32 code = 1;
  string message = 2;
  repeated SystemModule data = 3;
  message SystemModule {
    int32 id = 1;
    string name = 2;
  }
  int32 count = 4;
}

service ResourceService {
  rpc FindResources(FindResourcesRequest) returns (FindResourcesResponse) {}
  rpc SaveResourcesAndPermissions(SaveResourcesAndPermissionsRequest) returns (StringDataResponse) {}
}

message FindResourcesRequest {
  int32 systemModuleId = 1;
  int32 pageNumber = 2;
  int32 pageSize = 3;
}

message SaveResourcesAndPermissionsRequest { string metadata = 1; }

message FindResourcesResponse {
  int32 code = 1;
  string message = 2;
  repeated Resource data = 3;
  message Resource {
    int32 id = 1;
    string name = 2;
    string identify = 3;
    repeated Permission permissions = 4;
  }
  int32 count = 4;
}
message Permission {
  int32 id = 1;
  string name = 2;
  string action = 3;
  string identify = 4;
}

service RoleService {
  rpc CreateRole(CreateRoleRequest) returns (StringDataResponse) {}
  rpc DeleteRole(DeleteRoleRequest) returns (StringDataResponse) {}
  rpc UpdateRole(UpdateRoleRequest) returns (StringDataResponse) {}
  rpc RemovePermissionOfRole(RemovePermissionRequest) returns (StringDataResponse);
  rpc SetPermissionsToRole(SetPermissionToRoleRequest) returns (StringDataResponse) {}
  rpc FindRoles(FindDataByPageRequest) returns (FindRolesResponse) {}
  rpc FindOneRoleInfo(FindOneRoleInfoRequest) returns (FindOneRoleInfoResponse) {}
}

message CreateRoleRequest { string name = 1; }

message DeleteRoleRequest { int32 id = 1; }

message UpdateRoleRequest {
  int32 id = 1;
  string name = 2;
}

message RemovePermissionRequest {
  int32 roleId = 1;
  int32 permissionId = 2;
}

message SetPermissionToRoleRequest {
  int32 roleId = 1;
  repeated int32 permissionIds = 2;
}

message FindRolesResponse {
  int32 code = 1;
  string message = 2;
  repeated RoleData data = 3;
  int32 count = 4;
}
message RoleData {
  int32 id = 1;
  string name = 2;
}

message FindOneRoleInfoRequest { int32 roleId = 1; }

message FindOneRoleInfoResponse {
  int32 code = 1;
  string message = 2;
  OneRoleInfoData data = 3;
}
message OneRoleInfoData {
  int32 id = 1;
  string name = 2;
  repeated Permission permissions = 3;
  repeated InfoItem infoItems = 4;
}

service UserService {
  rpc Login(LoginRequest) returns (LoginResponse) {}
  rpc Register(RegisterRequest) returns (StringDataResponse) {}
  rpc CreateUser(CreateUserRequest) returns (StringDataResponse) {}
  rpc AddUserRole(AddOrDeleteUserRoleRequest) returns (StringDataResponse) {}
  rpc DeleteUserRole(AddOrDeleteUserRoleRequest) returns (StringDataResponse) {}
  rpc BanUser(DeleteUserRequest) returns (StringDataResponse) {}
  rpc RecycleUser(DeleteUserRequest) returns (StringDataResponse) {}
  rpc DeleteRecycledUser(DeleteUserRequest) returns (StringDataResponse) {}
  rpc RevertBannedUser(DeleteUserRequest) returns (StringDataResponse) {}
  rpc RevertRecycledUser(DeleteUserRequest) returns (StringDataResponse) {}
  rpc UpdateUserInfoById(UpdateUserInfoByIdRequest) returns (StringDataResponse) {}
  rpc UpdateCurrentUserInfo(UpdateCurrentUserInfoRequest) returns (StringDataResponse) {}
  rpc FindUserInfoByIds(FindUserInfoByIdsRequest) returns (FindUserInfoByIdsResponse) {}
  rpc FindCurrentUserInfo(FindCurrentUserInfoRequest) returns (FindCurrentUserInfoResponse) {}
  rpc FindRegisterUserInputInfo(PlaceholderRequest) returns (FindRegisterUserInputInfoResponse) {}
  rpc FindAllUsers(FindDataByPageRequest) returns (FindAllUsersResponse) {}
  rpc FindUsersInRole(FindUsersInRoleRequest) returns (FindUsersInRoleResponse) {}
  rpc FindUsersInOrganization(FindUsersInOrganizationRequest) returns (FindUsersInOrganizationResponse) {}
  rpc FindOneWithRolesAndPermissions(FindOneWithRolesAndPermissionsRequest) returns (FindOneWithRolesAndPermissionsResponse) {}
  rpc AddPermissionToUser(AddOrDeleteUserPermRequest) returns (StringDataResponse) {}
  rpc DeletePermissionOfUser(AddOrDeleteUserPermRequest) returns (StringDataResponse) {}
}

message LoginRequest {
  string username = 1;
  string password = 2;
}

message LoginResponse {
  int32 code = 1;
  string message = 2;
  LoginResponseData data = 3;
  message LoginResponseData {
    TokenInfo tokenInfo = 1;
    UserData userInfoData = 2;
  }
  message TokenInfo {
    string accessToken = 1;
    int32 expiresIn = 2;
  }
}

message RegisterRequest {
  RegisterUserInput registerUserInput = 1;
  message RegisterUserInput {
    string username = 1;
    string email = 2;
    string mobile = 3;
    string password = 4;
    repeated CreateUserInfoInfoKV infoKVs = 5;
  }
}

message CreateUserRequest {
  CreateUserInput createUserInput = 1;
  message CreateUserInput {
    string username = 1;
    string email = 2;
    string mobile = 3;
    string password = 4;
    bool banned = 5;
    repeated CreateUserInfoInfoKV infoKVs = 6;
    repeated int32 roleIds = 7;
    repeated int32 organizationIds = 8;
  }
}

message CreateUserInfoInfoKV {
  int32 infoItemId = 1;
  string userInfoValue = 2;
}

message AddOrDeleteUserRoleRequest {
  int32 userId = 1;
  int32 roleId = 2;
}

message DeleteUserRequest { int32 userId = 1; }

message UpdateUserInfoByIdRequest {
  int32 userId = 1;
  UpdateUserInput updateUserInput = 2;
}

message UpdateUserInput {
  string username = 1;
  string email = 2;
  string mobile = 3;
  string password = 4;
  bool banned = 5;
  bool recycle = 6;
  repeated UpdateUserInfoKV infoKVs = 7;
  repeated ChangedUserRoleOrOrganization roleIds = 8;
  repeated ChangedUserRoleOrOrganization organizationIds = 9;
  message ChangedUserRoleOrOrganization {
    int32 before = 1;
    int32 after = 2;
  }
}

message UpdateUserInfoKV {
  int32 userInfoId = 1;
  string userInfoValue = 2;
  int32 infoItemId = 3;
}

message UpdateCurrentUserInfoRequest {
  int32 userId = 1;
  UpdateCurrentUserInput updateCurrentUserInput = 2;
}

message UpdateCurrentUserInput {
  string username = 1;
  string email = 2;
  string mobile = 3;
  string password = 4;
  repeated UpdateUserInfoKV infoKVs = 5;
}

message FindUserInfoByIdsRequest { repeated int32 userIds = 1; }

message FindUserInfoByIdsResponse {
  int32 code = 1;
  string message = 2;
  repeated UserData data = 3;
}
message UserData {
  int32 id = 1;
  string username = 2;
  string email = 3;
  string mobile = 4;
  bool banned = 5;
  bool recycle = 6;
  string createdAt = 7;
  string updatedAt = 8;
  repeated UserRoleData userRoles = 9;
  repeated UserOrganizationData userOrganizations = 10;
  repeated UserInfoData userInfos = 11;
}
message UserRoleData {
  int32 id = 1;
  string name = 2;
}
message UserOrganizationData {
  int32 id = 1;
  string name = 2;
}
message UserInfoData {
  int32 id = 1;
  int32 order = 2;
  int32 infoItemId = 3;
  string type = 4;
  string name = 5;
  string value = 6;
  string description = 7;
  bool registerDisplay = 8;
  bool informationDisplay = 9;
}

message FindCurrentUserInfoRequest { int32 userId = 1; }

message FindCurrentUserInfoResponse {
  int32 code = 1;
  string message = 2;
  UserData data = 3;
}

message FindRegisterUserInputInfoResponse {
  int32 code = 1;
  string message = 2;
  repeated InfoItem data = 3;
}

message FindAllUsersResponse {
  int32 code = 1;
  string message = 2;
  repeated UserData data = 3;
  int32 count = 4;
}

message FindUsersInRoleRequest {
  int32 roleId = 1;
  int32 pageNumber = 2;
  int32 pageSize = 3;
}

message FindUsersInRoleResponse {
  int32 code = 1;
  string message = 2;
  repeated UserData data = 3;
  int32 count = 4;
}

message FindUsersInOrganizationRequest {
  int32 organizationId = 1;
  int32 pageNumber = 2;
  int32 pageSize = 3;
}

message FindUsersInOrganizationResponse {
  int32 code = 1;
  string message = 2;
  repeated UserData data = 3;
  int32 count = 4;
}

message FindOneWithRolesAndPermissionsRequest { string username = 1; }

message FindOneWithRolesAndPermissionsResponse {
  int32 code = 1;
  string message = 2;
  UserRoleAndPermissionData data = 3;
  message UserRoleAndPermissionData {
    int32 id = 1;
    string username = 2;
    string email = 3;
    string mobile = 4;
    bool banned = 5;
    bool recycle = 6;
    string createdAt = 7;
    string updatedAt = 8;
    repeated UserRole roles = 9;
    message UserRole {
      int32 id = 1;
      string name = 2;
      repeated Permission permissions = 3;
    }
    repeated PersonalPermission personalPermissions = 10;
    message PersonalPermission {
      int32 id = 1;
      string status = 2;
      Permission permission = 3;
    }
  }
}

message AddOrDeleteUserPermRequest {
  int32 userId = 1;
  int32 permissionId = 2;
}