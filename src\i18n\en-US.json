{"organization_manage": "organization_manage", "user_manage": "user_manage", "role_manage": "role_manage", "resource_manage": "resource_manage", "system_module_manage": "system_module_manage", "info_group_manage": "info_group_manage", "info_item_manage": "info_item_manage", "find_root_organizations": "find_root_organizations", "find_all_organizations": "find_all_organizations", "find_children_organizations": "find_children_organizations", "create_organization": "create_organization", "update_organization": "update_organization", "delete_organization": "delete_organization", "add_users_to_organization": "add_users_to_organization", "delete_user_from_organization": "delete_user_from_organization", "create_user": "create_user", "add_user_role": "add_user_role", "add_permission_to_user": "add_permission_to_user", "delete_permission_of_user": "delete_permission_of_user", "delete_user_role": "delete_user_role", "recycle_user": "recycle_user", "delete_recycled_user": "delete_recycled_user", "update_user_info_by_id": "update_user_info_by_id", "find_user_info_by_ids": "find_user_info_by_ids", "find_users_in_role": "find_users_in_role", "find_users_in_organization": "find_users_in_organization", "create_role": "create_role", "delete_role": "delete_role", "update_role": "update_role", "remove_permission_of_role": "remove_permission_from_role", "set_permissions_to_role": "set_permissions_to_role", "find_roles": "find_roles", "find_one_role_info": "find_one_role_info", "find_resources": "find_resources", "find_system_modules": "find_system_modules", "create_info_group": "create_info_group", "delete_info_group": "delete_info_group", "update_info_group": "update_info_group", "add_info_item_to_info_group": "add_info_item_to_info_group", "delete_into_item_from_info_group": "delete_into_item_from_info_group", "find_all_info_group": "find_all_info_group", "find_info_items_by_group_id": "find_info_items_by_group_id", "create_info_item": "create_info_item", "delete_info_item": "delete_info_item", "update_info_item": "update_info_item", "find_all_info_item": "find_all_info_item", "Request header lacks authorization parameters，it should be: Authorization": "Request header lacks authorization parameters，it should be: Authorization", "The authorization code prefix is incorrect. it should be: Bearer": "The authorization code prefix is incorrect. it should be: Bearer", "The authorization code is incorrect": "The authorization code is incorrect", "The authorization code has expired": "The authorization code has expired", "You are not authorized to access": "You are not authorized to access", "ban_user": "ban_user", "revert_banned_user": "revert_banned_user", "revert_recycled_user": "revert_recycled_user", "UserModule": "UserModule", "find_all_users": "find_all_users"}