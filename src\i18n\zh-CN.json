{"organization_manage": "组织管理", "user_manage": "用户管理", "role_manage": "角色管理", "resource_manage": "资源管理", "system_module_manage": "系统模块管理", "info_group_manage": "信息组管理", "info_item_manage": "信息项管理", "find_root_organizations": "查询根组织", "find_all_organizations": "查询所有组织", "find_children_organizations": "查询组织下的子组织", "create_organization": "创建组织", "update_organization": "更新组织", "delete_organization": "删除组织", "add_users_to_organization": "向组织添加用户", "delete_user_from_organization": "删除组织中的用户", "create_user": "创建用户", "add_user_role": "添加用户角色", "add_permission_to_user": "添加用户个人权限", "delete_permission_of_user": "删除用户权限", "delete_user_role": "删除用户角色", "recycle_user": "删除用户到回收站", "delete_recycled_user": "删除回收站内的用户", "update_user_info_by_id": "更新指定用户信息", "find_user_info_by_ids": "查询指定用户信息", "find_users_in_role": "查询角色下的用户", "find_users_in_organization": "查询组织下的用户", "create_role": "创建角色", "delete_role": "删除角色", "update_role": "更新角色", "remove_permission_of_role": "删除角色权限", "set_permissions_to_role": "设置角色权限", "find_roles": "查询所有角色", "find_one_role_info": "查询角色信息", "find_resources": "查询资源", "find_system_modules": "查询系统模块", "create_info_group": "创建信息组", "delete_info_group": "删除信息组", "update_info_group": "更新信息组", "add_info_item_to_info_group": "向信息组添加信息项", "delete_into_item_from_info_group": "删除信息组中的信息项", "find_all_info_group": "查询所有信息组", "find_info_items_by_group_id": "查询信息组中的信息项", "create_info_item": "创建信息项", "delete_info_item": "删除信息项", "update_info_item": "更新信息项", "find_all_info_item": "查询所有信息项", "Request header lacks authorization parameters，it should be: Authorization": "请求头缺少授权参数，授权参数名称为 Authorization", "The authorization code prefix is incorrect. it should be: Bearer": "授权码前缀有误，前缀为 Bearer", "The authorization code is incorrect": "授权码错误", "The authorization code has expired": "授权码已过期", "You are not authorized to access": "您无权访问", "ban_user": "封禁用户", "revert_banned_user": "解封用户", "revert_recycled_user": "恢复用户", "UserModule": "用户模块", "find_all_users": "查询所有用户"}