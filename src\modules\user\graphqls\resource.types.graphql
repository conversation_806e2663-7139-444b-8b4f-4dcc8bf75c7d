type Query {
    """Query The specifed system module's all resources and it's permissions"""
    findResources(systemModuleId: Int!, pageNumber: Int, pageSize: Int): ResourcesResult
}

type ResourcesResult {
    code: Int
    message: String
    data: [ResourceData]
    count: Int
}

type ResourceData {
    id: Int
    name: String
    identify: String
    permissions: [PermissionData]
}

type PermissionData {
    id: Int
    name: String
    action: String
    identify: String
}