type Query {
    """Query all roles"""
    findRoles(pageNumber: Int, pageSize: Int): RolesResult
    """Query a specified role's details"""
    findOneRoleInfo(roleId: Int!): RoleInfoResult
}

type Mutation {
    """Create a role"""
    createRole(name: String!): UserModuleCommonResult
    """Update the specified role's name"""
    updateRole(id: Int!, name: String!): UserModuleCommonResult
    """Delete the specified role"""
    deleteRole(id: Int!): UserModuleCommonResult
    """Remove a permission from role"""
    removePermissionOfRole(roleId: Int!, permissionId: Int!): UserModuleCommonResult
    """Set permissions for a specified role"""
    setPermissionsToRole(roleId: Int!, permissionIds: [Int]!): UserModuleCommonResult
}

type RolesResult {
    code: Int
    message: String
    data: [Role]
    count: Int
}

type Role {
    id: Int
    name: String
}

type RoleInfoResult {
    code: Int
    message: String
    data: RoleInfoData
}

type RoleInfoData {
    id: Int
    name: String
    permissions: [PermissionData]
    infoItems: [InfoItemData]
}