type Query {
    """Ordinary user login"""
    login(username: String!, password: String!): LoginResponse
    """Admin login"""
    adminLogin(username: String!, password: String!): LoginResponse
    """Query the information items required for ordinary users to register"""
    findRegisterUserInfoItem: RegisterUserInfoItemResult
    """Query the currently logged in user information"""
    findCurrentUserInfo: UserInfoResult
    """Query user information by ID array"""
    findUserInfoByIds(userIds: [Int]!): UsersInfoResult
    """Query all users"""
    findAllUsers(pageNumber: Int, pageSize: Int): UsersInfoResult
    """Query all user information under the specified role ID"""
    findUsersInRole(roleId: Int!, pageNumber: Int, pageSize: Int): UsersInfoResult
    """Query all user information under the specified organization ID"""
    findUsersInOrganization(organizationId: Int!, pageNumber: Int, pageSize: Int): UsersInfoResult
}

type Mutation {
    """Ordinary user registration"""
    register(registerUserInput: RegisterUserInput): UserModuleCommonResult
    """Create user"""
    createUser(createUserInput: CreateUserInput): UserModuleCommonResult
    """Add a role to the user"""
    addUserRole(userId: Int!, roleId: Int!): UserModuleCommonResult
    """Add a personal permission to user"""
    addPermissionToUser(userId: Int!, permissionId: Int!): UserModuleCommonResult
    """Delete a permission of specified user"""
    deletePermissionOfUser(userId: Int!, permissionId: Int!): UserModuleCommonResult
    """Ban user"""
    banUser(userId: Int!): UserModuleCommonResult
    """Delete a role from the user"""
    deleteUserRole(userId: Int!, roleId: Int!): UserModuleCommonResult
    """Delete user to recycle bin"""
    recycleUser(userId: Int!): UserModuleCommonResult
    """Delete user in the recycle bin"""
    deleteRecycledUser(userId: Int!): UserModuleCommonResult
    """Revert banned user"""
    revertBannedUser(userId: Int!): UserModuleCommonResult
    """Revert recycled user"""
    revertRecycledUser(userId: Int!): UserModuleCommonResult
    """Specify user information by ID update"""
    updateUserInfoById(userId: Int!, updateUserInput: UpdateUserInput): UserModuleCommonResult
    """Update current login user information"""
    updateCurrentUserInfo(updateCurrentUserInput: UpdateCurrentUserInput): UserModuleCommonResult
}

input RegisterUserInput {
    username: String
    email: String
    mobile: String
    password: String!
    """Information item key-value pair, key is the ID of the information item (infoItem.id), and the value is the value of the information item (userInfo.value)"""
    infoKVs: [RegisterOrCreateInfoKV]
}

input CreateUserInput {
    username: String
    email: String
    mobile: String
    password: String!
    banned: Boolean
    """Information item key-value pair, key is the ID of the information item (infoItem.id), and the value is the value of the information item (userInfo.value)"""
    infoKVs: [RegisterOrCreateInfoKV]
    roleIds: [Int]
    organizationIds: [Int]
}

input UpdateUserInput {
    email: String
    mobile: String
    password: String
    banned: Boolean
    recycle: Boolean
    """Information item value key-value pair, key is the ID of the user information item (userInfo.id), and value is the value of the information item (userInfo.value)"""
    infoKVs: [UpdateInfoKV]
    """An array of role ID objects owned, each item must contain the role ID (before) before the update, and the role ID (after) after the update"""
    roleIds: [RoleOrOrganizationIdData]
    """An array of organization ID objects, each of which must contain the organization ID (before) before the update, and the organization ID (after) after the update"""
    organizationIds: [RoleOrOrganizationIdData]
}

input UpdateCurrentUserInput {
    email: String
    mobile: String
    password: String
    """Information item value key-value pair, key is the ID of the user information item (userInfo.id), and value is the value of the information item (userInfo.value)"""
    infoKVs: [UpdateInfoKV]
}

input RegisterOrCreateInfoKV {
    """Information item ID"""
    infoItemId: Int
    """Information item value"""
    userInfoValue: String
}

input UpdateInfoKV {
    """ID of the item value(userInfo.id)"""
    userInfoId: Int
    """Information item value(userInfo.value)"""
    userInfoValue: String
    """Information item ID(infoItem.id)"""
    infoItemId: Int
}

input RoleOrOrganizationIdData {
    """Update previous ID"""
    before: Int
    """Updated ID"""
    after: Int
}

type LoginResponse {
    code: Int
    message: String
    data: AccessToken
}

type AccessToken {
    accessToken: String
    expiresIn: Int
}

type UserInfoResult {
    code: Int
    message: String
    data: UserInfoData
}

type UsersInfoResult {
    code: Int
    message: String
    data: [UserInfoData]
}

type UserInfoData {
    userId: Int
    username: String
    email: String
    mobile: String
    banned: Boolean
    recycle: Boolean
    createTime: String
    updateTime: String
    userRoles: [Role]
    userOrganizations: [Organization]
    userInfos: [UserInfo]
}

type UserInfo {
    """The user information item ID, if and only if it is null, represents that the value of this information item in the update user operation is to be created. In this case, the relationId needs to be passed into the UpdateInfoKV object, and the key in the UpdateInfoKV object needs to be transmitted null"""
    id: Int
    order: Int
    """Information item ID(infoItem.id)"""
    relationId: Int
    type: String
    name: String
    value: String
    description: String
    registerDisplay: Boolean
    informationDisplay: Boolean
}

type RegisterUserInfoItemResult {
    code: Int
    message: String
    data: [InfoItemData]
}