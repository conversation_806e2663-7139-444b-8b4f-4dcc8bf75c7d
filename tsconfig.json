{"compilerOptions": {"allowJs": false, "baseUrl": "./", "declaration": true, "emitDecoratorMetadata": true, "experimentalDecorators": true, "module": "commonjs", "noImplicitAny": false, "noLib": false, "lib": ["es2017", "esnext.asynciterable"], "noUnusedLocals": false, "removeComments": true, "strict": false, "strictPropertyInitialization": false, "target": "es2017", "outDir": "dist"}, "include": ["src/*.ts", "src/**/*.ts"], "exclude": ["node_modules", "test"]}