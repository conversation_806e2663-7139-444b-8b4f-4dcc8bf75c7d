{"defaultSeverity": "error", "extends": ["tslint:recommended"], "linterOptions": {"exclude": ["node_modules/**", "src/grpc/generated.ts"]}, "jsRules": {"no-unused-expression": true}, "rules": {"array-type": [true, "array"], "ban-types": {"options": [["Object", "Avoid using the `Object` type. Did you mean `object`?"], ["Function", "Avoid using the `Function` type. Prefer a specific function type, like `() => void`, or use `ts.AnyFunction`."], ["Boolean", "Avoid using the `Boolean` type. Did you mean `boolean`?"], ["Number", "Avoid using the `Number` type. Did you mean `number`?"], ["String", "Avoid using the `String` type. Did you mean `string`?"]]}, "class-name": true, "comment-format": [true, "check-space"], "curly": [true, "ignore-same-line"], "indent": [true, "spaces", 4], "max-line-length": [true, 200], "quotemark": [true, "single"], "semicolon": [true, "always"], "interface-name": [false], "interface-over-type-literal": true, "jsdoc-format": true, "linebreak-style": false, "next-line": false, "no-inferrable-types": true, "no-internal-module": true, "no-null-keyword": true, "no-switch-case-fall-through": true, "no-trailing-whitespace": [true, "ignore-template-strings"], "no-var-keyword": true, "object-literal-shorthand": true, "one-line": [true, "check-open-brace", "check-whitespace"], "prefer-const": true, "prefer-conditional-expression": [true, "check-else-if"], "prefer-for-of": true, "space-within-parens": true, "triple-equals": true, "typedef-whitespace": [true, {"call-signature": "nospace", "index-signature": "nospace", "parameter": "nospace", "property-declaration": "nospace", "variable-declaration": "nospace"}], "whitespace": [true, "check-branch", "check-decl", "check-operator", "check-separator", "check-type"], "no-implicit-dependencies": false, "object-literal-key-quotes": [true, "consistent-as-needed"], "variable-name": [true, "ban-keywords", "check-format", "allow-leading-underscore", "allow-pascal-case"], "arrow-parens": false, "arrow-return-shorthand": true, "forin": false, "member-access": false, "no-conditional-assignment": false, "no-console": false, "no-debugger": false, "no-empty": false, "no-empty-interface": false, "no-eval": false, "no-object-literal-type-assertion": false, "no-shadowed-variable": false, "no-submodule-imports": false, "no-var-requires": false, "ordered-imports": false, "radix": false, "trailing-comma": false, "align": false, "eofline": false, "no-consecutive-blank-lines": false, "space-before-function-paren": false, "ban-comma-operator": false, "max-classes-per-file": false, "member-ordering": false, "no-angle-bracket-type-assertion": false, "no-bitwise": false, "no-namespace": false, "no-reference": false, "object-literal-sort-keys": false, "one-variable-per-declaration": false, "type-operator-spacing": false, "no-type-assertion-whitespace": false, "object-literal-surrounding-space": false, "no-increment-decrement": false, "no-in-operator": false, "no-double-space": false, "no-unnecessary-type-assertion-2": false, "no-bom": false, "boolean-trivia": false, "debug-assert": false, "no-unused-expression": false}}